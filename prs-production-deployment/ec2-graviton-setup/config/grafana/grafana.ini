# PRS Grafana Configuration for EC2 Graviton
# Optimized for 4GB memory and ARM64 architecture

[default]
# Instance name
instance_name = prs-ec2-graviton

[paths]
# Data directory
data = /var/lib/grafana
# Logs directory
logs = /var/log/grafana
# Plugins directory
plugins = /var/lib/grafana/plugins
# Provisioning directory
provisioning = /etc/grafana/provisioning

[server]
# Protocol (http, https, h2, socket)
protocol = http
# The ip address to bind to, empty will bind to all interfaces
http_addr =
# The http port to use
http_port = 3000
# The public facing domain name used to access grafana from a browser
domain = localhost
# Redirect to correct domain if host header does not match domain
enforce_domain = false
# The full public facing url you use in browser, used for redirects and emails
root_url = %(protocol)s://%(domain)s:%(http_port)s/grafana/
# Serve <PERSON> from subpath specified in `root_url` setting
serve_from_sub_path = true

[database]
# Database type (sqlite3, mysql, postgres)
type = sqlite3
# Database host (only for mysql and postgres)
host = 127.0.0.1:3306
# Database name
name = grafana
# Database user (only for mysql and postgres)
user = root
# Database password (only for mysql and postgres)
password =
# Database path (only for sqlite3)
path = /var/lib/grafana/grafana.db
# Max idle connections in the connection pool
max_idle_conn = 2
# Max open connections in the connection pool
max_open_conn = 5
# Connection max lifetime in seconds
conn_max_lifetime = 14400

[session]
# Session provider (memory, file, redis, mysql, postgres)
provider = file
# Provider config options
provider_config = sessions
# Session cookie name
cookie_name = grafana_sess
# Session cookie secure
cookie_secure = false
# Session lifetime in seconds
session_life_time = 86400

[dataproxy]
# This enables data proxy logging
logging = false
# How long the data proxy waits before timing out
timeout = 30
# How many seconds the data proxy waits before sending a keepalive probe
keep_alive_seconds = 30

[analytics]
# Server reporting, sends usage counters to stats.grafana.org every 24 hours
reporting_enabled = false
# Set to false to disable all checks to https://grafana.net
check_for_updates = false

[security]
# Default admin user
admin_user = admin
# Default admin password
admin_password = admin123
# Used for signing
secret_key = SW2YcwTIb9zpOOhoPsMm
# Disable gravatar profile images
disable_gravatar = true
# Data source proxy whitelist (ip_or_domain:port separated by spaces)
data_source_proxy_whitelist =
# Disable protection against brute force login attempts
disable_brute_force_login_protection = false
# Cookie SameSite attribute
cookie_samesite = lax

[snapshots]
# Snapshot sharing options
external_enabled = false
external_snapshot_url = https://snapshots-origin.raintank.io
external_snapshot_name = Publish to snapshot.raintank.io

[dashboards]
# Number of versions to keep (per dashboard)
versions_to_keep = 20
# Minimum refresh interval
min_refresh_interval = 5s

[users]
# Disable user signup / registration
allow_sign_up = false
# Allow non admin users to create organizations
allow_org_create = false
# Set to true to automatically assign new users to the default organization (id 1)
auto_assign_org = true
# Set this value to automatically add new users to the provided organization (if auto_assign_org above is set to true)
auto_assign_org_id = 1
# Default role new users will be automatically assigned
auto_assign_org_role = Viewer
# Require email validation before sign up completes
verify_email_enabled = false
# Background text for the user field on the login page
login_hint = email or username
# Default UI theme ("dark" or "light")
default_theme = dark
# External user management
external_manage_link_url =
external_manage_link_name =
external_manage_info =
# Viewers can edit/inspect dashboard settings in the browser
viewers_can_edit = false
# Editors can administrate dashboard, folders and teams they create
editors_can_admin = false

[auth]
# Login cookie name
login_cookie_name = grafana_session
# The maximum lifetime (duration) an authenticated user can be inactive before being required to login at next visit
login_maximum_inactive_lifetime_duration =
# The maximum lifetime (duration) an authenticated user can be logged in since login time before being required to login
login_maximum_lifetime_duration =
# How often should auth tokens be rotated for authenticated users when being active
token_rotation_interval_minutes = 10
# Set to true to disable (hide) the login form, useful if you use OAuth
disable_login_form = false
# Set to true to disable the signout link in the side menu
disable_signout_menu = false
# URL to redirect the user to after sign out
signout_redirect_url =
# Set to true to attempt login with OAuth automatically, skipping the login screen
oauth_auto_login = false
# OAuth state max age cookie duration in seconds
oauth_state_cookie_max_age = 600

[auth.anonymous]
# Enable anonymous access
enabled = false
# Specify organization name that should be used for unauthenticated users
org_name = Main Org.
# Specify role for unauthenticated users
org_role = Viewer

[auth.basic]
# Enable basic authentication
enabled = true

[log]
# Log mode (console, file, syslog)
mode = console
# Log level (trace, debug, info, warn, error, critical)
level = info
# Optional settings to set different levels for specific loggers
filters =

[log.console]
# Log line format (text, console, json)
format = console

[metrics]
# Enable metrics
enabled = true
# Send internal metrics to Graphite
interval_seconds = 10

[metrics.graphite]
# Enable by setting the address setting (ex localhost:2003)
address =
prefix = prod.grafana.%(instance_name)s.

[tracing.jaeger]
# Enable by setting the address sending traces to jaeger (ex localhost:14268)
address =
# Tag that will always be included in when creating new spans
always_included_tag =
# Type specifies the type of the sampler: const, probabilistic, rateLimiting, or remote
sampler_type = const
# jaeger samplerconfig param
sampler_param = 1

[grafana_net]
url = https://grafana.net

[alerting]
# Disable alerting engine & UI features
enabled = false
# Makes it possible to turn off alert rule execution but alerting UI is visible
execute_alerts = false
# Default setting for new alert rules
error_or_timeout = alerting
# Default setting for how Grafana handles nodata or null values in alerting
nodata_or_nullvalues = no_data
# Alert notifications can include images, but rendering images can be resource intensive
concurrent_render_limit = 5

[explore]
# Enable the Explore section
enabled = true

[panels]
# Enable or disable panel flot graph (legacy graph panel)
enable_alpha = false

[plugins]
# Enable or disable installing / uninstalling / updating plugins directly from within Grafana
enable_alpha = false
# Enter a comma-separated list of plugin identifiers to identify plugins that are allowed to be loaded even if they lack a valid signature
allow_loading_unsigned_plugins =

[enterprise]
# Path to a valid Grafana Enterprise license.jwt file
license_path =
