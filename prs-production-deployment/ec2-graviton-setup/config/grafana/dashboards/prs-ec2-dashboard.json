{"dashboard": {"id": null, "title": "PRS EC2 Graviton Monitoring", "tags": ["prs", "ec2", "graviton", "production"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "up", "legendFormat": "Services Up"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Memory Usage", "type": "timeseries", "targets": [{"expr": "container_memory_usage_bytes{name=~\"prs-ec2-.*\"}", "legendFormat": "{{name}}"}], "fieldConfig": {"defaults": {"unit": "bytes"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "CPU Usage", "type": "timeseries", "targets": [{"expr": "rate(container_cpu_usage_seconds_total{name=~\"prs-ec2-.*\"}[5m])", "legendFormat": "{{name}}"}], "fieldConfig": {"defaults": {"unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "HTTP Requests", "type": "timeseries", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{method}} {{status}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "schemaVersion": 30, "version": 1}}