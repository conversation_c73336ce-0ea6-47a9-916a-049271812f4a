# PRS EC2 Graviton Medium Configuration
# Copy this file to .env and customize for your EC2 instance
# Optimized for 2 cores, 4GB memory

# EC2 Instance Configuration (Internal Access Only)
DOMAIN=your-domain.com
HTTP_PORT=80
HTTPS_PORT=443
VERSION=latest

# Cloudflare Tunnel Configuration
CLOUDFLARE_TUNNEL_TOKEN=your-cloudflare-tunnel-token-here
GRAFANA_SUBDOMAIN=grafana
ADMINER_SUBDOMAIN=adminer
PORTAINER_SUBDOMAIN=portainer

# Public Access Control
ENABLE_PUBLIC_ACCESS=false
ALLOWED_IPS=127.0.0.1,::1

# Database Configuration
POSTGRES_DB=prs_production
POSTGRES_USER=prs_user
POSTGRES_PASSWORD=CHANGE_THIS_STRONG_PASSWORD_123!
DIALECT=postgres

# Database Pool Configuration (Optimized for 4GB Memory)
POOL_MIN=1
POOL_MAX=3
POOL_ACQUIRE=30000
POOL_IDLE=10000
POOL_EVICTION=20000

# Application Security (CHANGE THESE FOR PRODUCTION!)
JWT_SECRET=your-super-secure-jwt-secret-key-32-chars-minimum
ENCRYPTION_KEY=your-super-secure-encryption-key-32-chars
OTP_KEY=your-base64-encoded-otp-key-64-bytes-minimum
PASS_SECRET=your-password-secret-key
BYPASS_OTP=false

# CORS Configuration (Production)
CORS_ORIGIN=https://your-ec2-domain.com,https://www.your-ec2-domain.com

# External API Configuration
CITYLAND_API_URL=https://your-cityland-api.com
CITYLAND_ACCOUNTING_URL=https://your-cityland-accounting.com

# Root User (Initial Admin)
ROOT_USER_NAME=admin
ROOT_USER_EMAIL=<EMAIL>
ROOT_USER_PASSWORD=CHANGE_THIS_ADMIN_PASSWORD_123!

# Monitoring
GRAFANA_ADMIN_PASSWORD=CHANGE_THIS_GRAFANA_PASSWORD_123!
LOG_LEVEL=info

# Container Resource Limits (Optimized for 4GB EC2)
BACKEND_MEMORY_LIMIT=1g
FRONTEND_MEMORY_LIMIT=512m
POSTGRES_MEMORY_LIMIT=1.5g
GRAFANA_MEMORY_LIMIT=256m
PROMETHEUS_MEMORY_LIMIT=256m

# Timezone
TZ=Asia/Manila

# Container Update Policy
RESTART_POLICY=unless-stopped

# EC2 Network Configuration
NETWORK_NAME=prs_ec2_network
NETWORK_SUBNET=172.22.0.0/16

# Production Mode Settings
NODE_ENV=production
VITE_APP_ENVIRONMENT=production
VITE_APP_ENABLE_DEVTOOLS=false

# Database Settings (Optimized for 4GB Memory)
POSTGRES_SHARED_BUFFERS=128MB
POSTGRES_EFFECTIVE_CACHE_SIZE=512MB
POSTGRES_WORK_MEM=4MB
POSTGRES_MAINTENANCE_WORK_MEM=32MB

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=7

# Monitoring Configuration (Memory Optimized)
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
NODE_EXPORTER_ENABLED=false
CADVISOR_ENABLED=false

# Production Security Flags
SKIP_SSL_VERIFICATION=false
ENABLE_MOCK_APIS=false
ENABLE_TEST_DATA=false
ENABLE_DEBUG_LOGS=false

# File Upload Configuration
MAX_FILE_SIZE=50m
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif

# Email Configuration (Configure with your SMTP provider)
SMTP_HOST=your-smtp-server.com
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASSWORD=your-smtp-password
SMTP_FROM="PRS System <<EMAIL>>"

# SSL Configuration (Let's Encrypt or custom certificates)
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem
SSL_DHPARAM_PATH=./ssl/dhparam.pem

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# Performance Tuning for EC2 Graviton
# ARM64 optimizations
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1

# Memory management
NODEJS_MAX_OLD_SPACE_SIZE=768
POSTGRES_EFFECTIVE_IO_CONCURRENCY=200

# Logging Configuration
LOG_RETENTION_DAYS=7
LOG_MAX_SIZE=100m

# Adminer Configuration
ADMINER_PORT=8080

# Grafana Configuration
GRAFANA_PORT=3001

# Development Features (Disabled for Production)
ENABLE_HOT_RELOAD=false
ENABLE_CORS_ALL=false

# Security Headers
ENABLE_SECURITY_HEADERS=true
ENABLE_RATE_LIMITING=true

# Session Configuration
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# Cache Configuration
REDIS_ENABLED=false
CACHE_TTL=3600

# Monitoring Retention (Reduced for Memory)
PROMETHEUS_RETENTION_TIME=3d
PROMETHEUS_RETENTION_SIZE=500MB
GRAFANA_DATA_RETENTION=7d

# Backup Schedule (if enabled)
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_COMPRESSION=true

# EC2 Specific Settings
AWS_REGION=us-east-1
EC2_INSTANCE_TYPE=t4g.medium
EC2_ARCHITECTURE=arm64

# Docker Configuration
DOCKER_COMPOSE_VERSION=v2
DOCKER_RESTART_POLICY=unless-stopped

# Application Features
ENABLE_AUDIT_LOGS=true
ENABLE_METRICS=true
ENABLE_HEALTH_CHECKS=true

# Database Connection Settings
DB_CONNECTION_TIMEOUT=30000
DB_QUERY_TIMEOUT=60000
DB_IDLE_TIMEOUT=10000

# File System Settings
UPLOAD_MAX_FILES=10
UPLOAD_MAX_SIZE_PER_FILE=10m
UPLOAD_ALLOWED_EXTENSIONS=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif

# API Rate Limiting
API_RATE_LIMIT_WINDOW=15
API_RATE_LIMIT_MAX_REQUESTS=100

# Security Settings
ENABLE_HELMET=true
ENABLE_CORS_CREDENTIALS=true
SECURE_COOKIES=true
SAME_SITE_COOKIES=strict

# Monitoring Endpoints
HEALTH_CHECK_PATH=/health
METRICS_PATH=/metrics
STATUS_PATH=/status

# Error Handling
ERROR_STACK_TRACE=false
ERROR_DETAILS=false

# Performance Monitoring
ENABLE_APM=false
APM_SERVICE_NAME=prs-ec2

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false

# Feature Flags
FEATURE_NEW_UI=true
FEATURE_ADVANCED_SEARCH=true
FEATURE_BULK_OPERATIONS=true

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="System is under maintenance. Please try again later."

# Cleanup Settings
AUTO_CLEANUP_LOGS=true
AUTO_CLEANUP_TEMP_FILES=true
CLEANUP_SCHEDULE="0 3 * * *"
