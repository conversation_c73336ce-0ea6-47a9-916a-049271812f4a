#!/bin/bash

# Fix Nginx Configuration Issues
# This script fixes nginx configuration errors and restarts the service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

main() {
    log_info "🔧 Fixing Nginx Configuration Issues"
    echo ""
    
    cd "$PROJECT_DIR"
    
    # Step 1: Test nginx configuration
    log_info "Step 1: Testing nginx configuration..."
    
    if docker run --rm -v "$PWD/nginx/nginx.conf:/etc/nginx/nginx.conf:ro" nginx:1.24-alpine nginx -t; then
        log_success "✅ Nginx configuration is valid"
    else
        log_error "❌ Nginx configuration has errors"
        echo ""
        echo "Configuration test output:"
        docker run --rm -v "$PWD/nginx/nginx.conf:/etc/nginx/nginx.conf:ro" nginx:1.24-alpine nginx -t 2>&1 || true
        exit 1
    fi
    
    # Step 2: Stop nginx container
    log_info "Step 2: Stopping nginx container..."
    docker-compose stop nginx 2>/dev/null || true
    docker rm prs-ec2-nginx 2>/dev/null || true
    
    # Step 3: Restart nginx with new configuration
    log_info "Step 3: Starting nginx with fixed configuration..."
    docker-compose up -d nginx
    
    # Step 4: Wait for nginx to start
    log_info "Step 4: Waiting for nginx to start..."
    sleep 10
    
    # Step 5: Check nginx status
    log_info "Step 5: Checking nginx status..."
    
    if docker ps | grep -q "prs-ec2-nginx.*Up"; then
        log_success "✅ Nginx is running"
        
        # Test nginx health
        if docker exec prs-ec2-nginx nginx -t 2>/dev/null; then
            log_success "✅ Nginx configuration loaded successfully"
        else
            log_error "❌ Nginx configuration has runtime errors"
            docker logs prs-ec2-nginx --tail 10
            exit 1
        fi
        
        # Test health endpoint
        if docker exec prs-ec2-nginx wget -q --spider --timeout=5 http://localhost:80/health 2>/dev/null; then
            log_success "✅ Nginx health endpoint is responding"
        else
            log_warning "⚠️  Nginx health endpoint not responding (this may be normal if backend is not ready)"
        fi
        
    else
        log_error "❌ Nginx failed to start"
        echo ""
        echo "Nginx logs:"
        docker logs prs-ec2-nginx --tail 20 2>&1 || true
        exit 1
    fi
    
    # Step 6: Restart dependent services
    log_info "Step 6: Restarting services that depend on nginx..."
    
    # Stop tunnel if running
    docker-compose --profile cloudflare stop cloudflared 2>/dev/null || true
    
    # Restart backend and frontend to ensure they can connect to nginx
    log_info "  Restarting backend and frontend..."
    docker-compose restart backend frontend
    sleep 10
    
    # Step 7: Restart Cloudflare Tunnel
    log_info "Step 7: Restarting Cloudflare Tunnel..."
    docker-compose --profile cloudflare up -d cloudflared
    sleep 10
    
    # Step 8: Final verification
    log_info "Step 8: Final verification..."
    
    # Check all services
    local services=("nginx" "backend" "frontend")
    local all_healthy=true
    
    for service in "${services[@]}"; do
        if docker ps | grep -q "prs-ec2-$service.*Up"; then
            log_success "  ✅ $service is running"
        else
            log_error "  ❌ $service is not running"
            all_healthy=false
        fi
    done
    
    # Check tunnel
    if docker ps | grep -q "prs-ec2-cloudflared.*Up"; then
        log_success "  ✅ Cloudflare Tunnel is running"
        
        # Check for recent errors
        if docker logs prs-ec2-cloudflared --tail 5 2>&1 | grep -q "ERR"; then
            log_warning "  ⚠️  Found recent errors in tunnel logs:"
            docker logs prs-ec2-cloudflared --tail 3 2>&1 | grep "ERR" || true
        else
            log_success "  ✅ No recent errors in tunnel logs"
        fi
    else
        log_warning "  ⚠️  Cloudflare Tunnel is not running"
        all_healthy=false
    fi
    
    echo ""
    if [ "$all_healthy" = true ]; then
        echo "🎉 Nginx Configuration Fix Complete!"
        echo ""
        log_info "All services are running properly."
        echo ""
        echo "You can now test your services:"
        
        if [ -f ".env" ]; then
            source .env
            echo "  - Main App: https://${DOMAIN:-your-domain.com}"
            echo "  - Adminer:  https://adminer.${DOMAIN:-your-domain.com}"
            echo "  - Portainer: https://portainer.${DOMAIN:-your-domain.com}"
            if [ "${GRAFANA_ENABLED:-true}" = "true" ]; then
                echo "  - Grafana:  https://grafana.${DOMAIN:-your-domain.com}"
            fi
        fi
    else
        echo "⚠️  Some services need attention."
        echo ""
        echo "Check logs with:"
        echo "  docker-compose logs nginx"
        echo "  docker-compose logs backend"
        echo "  docker-compose logs cloudflared"
    fi
    
    echo ""
    echo "Monitor logs with:"
    echo "  docker logs prs-ec2-nginx -f"
    echo "  docker logs prs-ec2-cloudflared -f"
}

# Run the fix
main "$@"
