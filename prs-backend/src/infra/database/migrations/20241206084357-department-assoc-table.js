module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('department_association_approvals', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      approvalTypeCode: {
        type: Sequelize.STRING(50),
        allowNull: false,
        references: {
          model: 'approval_types',
          key: 'code',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'approval_type_code',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      areaCode: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Area code for level 1 approvers only',
        field: 'area_code',
      },
      approverId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        field: 'approver_id',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('department_association_approvals', [
      'approval_type_code',
    ]);
    await queryInterface.addIndex('department_association_approvals', [
      'approver_id',
    ]);
    await queryInterface.addIndex('department_association_approvals', [
      'level',
    ]);
    await queryInterface.addIndex('department_association_approvals', [
      'area_code',
    ]);
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('department_association_approvals');
  },
};
