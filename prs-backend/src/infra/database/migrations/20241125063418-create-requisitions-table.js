'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('requisitions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      rsNumber: {
        type: Sequelize.STRING(8),
        allowNull: false,
        field: 'rs_number',
      },
      companyCode: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'company_code',
      },
      rsLetters: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'rs_letters',
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        field: 'created_by',
      },
      companyId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'companies',
          key: 'id',
        },
        field: 'company_id',
      },
      departmentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'departments',
          key: 'id',
        },
        field: 'department_id',
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'projects',
          key: 'id',
        },
        field: 'project_id',
      },
      dateRequired: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'date_required',
      },
      deliveryAddress: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'delivery_address',
      },
      purpose: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'purpose',
      },
      chargeTo: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'charge_to',
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        field: 'status',
      },
      assignedTo: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'assigned_to',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('requisitions');
  },
};
