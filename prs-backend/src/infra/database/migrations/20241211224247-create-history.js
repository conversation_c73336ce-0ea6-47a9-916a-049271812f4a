'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('histories', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      rsNumber: {
        type: Sequelize.STRING(8),
        allowNull: false,
        field: 'rs_number',
      },
      itemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'items',
          key: 'id',
        },
        field: 'item_id',
      },
      company: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'company',
      },
      project: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'project',
      },
      dateRequested: {
        allowNull: false,
        type: Sequelize.DATE,
        field: 'date_requested',
      },
      quantityRequested: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'quantity_requested',
      },
      price: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'price',
      },
      quantityDelivered: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'quantity_delivered',
      },
      dateDelivered: {
        allowNull: true,
        type: Sequelize.DATE,
        field: 'date_delivered',
      },
      type: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'type',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        field: 'created_at',
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        field: 'updated_at',
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('histories');
  },
};
