'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('requisition_item_lists', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        field: 'requisition_id',
      },
      ofmItemId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'ofm_item_id',
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'quantity',
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'notes',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('requisition_item_lists');
  }
};
