'use strict';

const { DISCOUNT_TYPE } = require('../../../domain/constants/canvassConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('canvass_item_suppliers', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      canvassItemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_items',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_item_id',
      },
      supplierId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'suppliers',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'supplier_id',
      },
      term: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      unitPrice: {
        type: Sequelize.DOUBLE,
        allowNull: false,
        field: 'unit_price',
      },
      discountType: {
        type: Sequelize.ENUM(Object.values(DISCOUNT_TYPE)),
        allowNull: false,
        defaultValue: DISCOUNT_TYPE.FIXED,
        field: 'discount_type',
      },
      isSelected: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_selected',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('canvass_item_suppliers', [
      'canvass_item_id',
    ]);
    await queryInterface.addIndex('canvass_item_suppliers', ['supplier_id']);
    await queryInterface.addIndex('canvass_item_suppliers', ['order']);

    await queryInterface.addIndex(
      'canvass_item_suppliers',
      ['canvass_item_id', 'supplier_id'],
      {
        unique: true,
        name: 'unique_canvass_item_supplier',
      },
    );

    await queryInterface.addIndex(
      'canvass_item_suppliers',
      ['canvass_item_id', 'order'],
      {
        unique: true,
        name: 'unique_canvass_item_order',
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('canvass_item_suppliers');
  },
};
