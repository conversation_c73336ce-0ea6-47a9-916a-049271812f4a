'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('histories', 'rs_letter', {
      type: Sequelize.STRING(2),
      allowNull: false,
      field: 'rs_letter',
      defaultValue: 'AA'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('histories', 'rs_letter');
  }
};
