'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('histories', 'rsNumber', {
      type: Sequelize.STRING(20),
      field: 'rs_number',
      allowNull: false,
    });

  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('histories', 'rsNumber', {
      type: Sequelize.STRING(8),
      field: 'rs_number',
      allowNull: false,
    });
  }
};
