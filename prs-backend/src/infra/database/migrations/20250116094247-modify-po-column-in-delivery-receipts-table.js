'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.removeColumn('delivery_receipts', 'po_number');
    await queryInterface.addColumn('delivery_receipts', 'po_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('delivery_receipts', 'po_id');
    await queryInterface.addColumn('delivery_receipts', 'po_number', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  }
};

