'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('requisition_return_histories', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
        references: {
          model: 'requisitions',
          key: 'id',
        },
      },
      drNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'dr_number',
      },
      item: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'item',
      },
      supplier: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'supplier',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      quantityOrdered: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'quantity_ordered',
      },
      quantityReturned: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'quantity_returned',
      },
      returnDate: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'return_date',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('requisition_return_histories');
  }
};
