'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('departments', 'assistant_manager_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    });

    await queryInterface.addColumn('departments', 'manager_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    });

    await queryInterface.addColumn(
      'departments',
      'department_division_head_id',
      {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
      },
    );

    await queryInterface.addIndex('departments', ['assistant_manager_id'], {
      name: 'departments_assistant_manager_id_idx',
    });

    await queryInterface.addIndex('departments', ['manager_id'], {
      name: 'departments_manager_id_idx',
    });

    await queryInterface.addIndex(
      'departments',
      ['department_division_head_id'],
      {
        name: 'departments_division_head_id_idx',
      },
    );
  },

  async down(queryInterface, Sequelize) {},
};
