'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('tom_items', { // tom = transfer of materials
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'name',
      },
      unit: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'unit',
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'quantity',
      },
      comment: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'comment',
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('tom_items');
  },
};
