'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.renameColumn('trades', 'trade_name', 'trade_name_old');

    await queryInterface.addColumn('trades', 'trade_name', {
      type: Sequelize.STRING(100),
      allowNull: false,
      defaultValue: '',
    });

    await queryInterface.sequelize.query(`
      UPDATE trades 
      SET trade_name = trade_name_old
    `);

    await queryInterface.changeColumn('trades', 'trade_name', {
      type: Sequelize.STRING(100),
      allowNull: false,
    });

    await queryInterface.removeColumn('trades', 'trade_name_old');
  },

  async down(queryInterface, Sequelize) {},
};
