'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('requisition_order_histories', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
        references: {
          model: 'requisitions',
          key: 'id',
        },
      },
      poNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'po_number',
      },
      supplier: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'supplier',
      },
      poPrice: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'po_price',
      },
      dateOrdered: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'date_ordered',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('requisition_order_history');
  }
};
