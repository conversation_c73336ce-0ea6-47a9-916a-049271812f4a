'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('requisition_badges', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
        references: {
          model: 'requisitions',
          key: 'id',
        },
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'created_by',
        references: {
          model: 'users',
          key: 'id',
        },
      },
      seenBy: {
        type: Sequelize.ARRAY(Sequelize.INTEGER),
        allowNull: true,
        field: 'seen_by',
        defaultValue: [],
      },
      model: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'model',
      },
      modelId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'model_id',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    });

    await queryInterface.addIndex('requisition_badges', ['requisition_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('requisition_badges');
  },
};
