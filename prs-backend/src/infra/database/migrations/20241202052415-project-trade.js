'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('projects_trades', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'project_id',
      },
      tradeId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'trades',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'trade_id',
      },
      engineerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'engineer_id',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('projects_trades', ['project_id']);
    await queryInterface.addIndex('projects_trades', ['trade_id']);
    await queryInterface.addIndex('projects_trades', ['engineer_id']);

    await queryInterface.addIndex(
      'projects_trades',
      ['project_id', 'trade_id'],
      {
        name: 'idx_projects_trades_project_trade',
      },
    );

    await queryInterface.addIndex(
      'projects_trades',
      ['project_id', 'trade_id', 'engineer_id'],
      {
        unique: true,
        name: 'unique_project_trade_engineer',
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('projects_trades');
  },
};
