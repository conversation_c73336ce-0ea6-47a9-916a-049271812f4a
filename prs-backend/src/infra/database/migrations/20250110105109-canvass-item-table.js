'use strict';

const {
  CANVASS_ITEM_STATUS,
} = require('../../../domain/constants/canvassConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('canvass_items', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      canvassRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_requisitions',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_requisition_id',
      },
      requisitionItemListId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisition_item_lists',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_item_list_id',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: CANVASS_ITEM_STATUS.NEW,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('canvass_items', ['canvass_requisition_id']);
    await queryInterface.addIndex('canvass_items', [
      'requisition_item_list_id',
    ]);

    await queryInterface.addIndex(
      'canvass_items',
      ['canvass_requisition_id', 'requisition_item_list_id'],
      {
        unique: true,
        name: 'unique_canvass_requisition_item',
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('canvass_items');
  },
};
